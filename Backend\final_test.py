#!/usr/bin/env python3
"""
Final test of the corrected query advanced endpoint.
"""

import requests
import json

def test_query_advanced_endpoint():
    """Test the query advanced endpoint with corrected configuration."""
    print("🎯 FINAL TEST: Query Advanced Endpoint")
    print("=" * 60)
    
    try:
        # Test 1: Basic connectivity
        print("1. Testing backend connectivity...")
        response = requests.get("http://localhost:8888/docs", timeout=10)
        if response.status_code == 200:
            print("   ✅ Backend accessible")
        else:
            print(f"   ❌ Backend not accessible (status: {response.status_code})")
            return False
        
        # Test 2: Simple query
        print("\n2. Testing simple query...")
        payload = {"question": "What is monitoring infrastructure?"}
        
        response = requests.post(
            "http://localhost:8888/query/advanced",
            json=payload,
            timeout=60
        )
        
        print(f"   Response status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            answer = result.get('answer', '')
            sources = result.get('sources', [])
            token_usage = result.get('token_usage', {})
            
            print(f"   Sources found: {len(sources)}")
            
            # Check if Bedrock is working
            if 'AWS Bedrock model' in answer and "couldn't access" in answer:
                print("   ❌ Still has Bedrock access issues")
                print(f"   Error answer: {answer[:150]}...")
                return False
            else:
                print("   ✅ Bedrock LLM working!")
                print(f"   Answer preview: {answer[:200]}...")
                
                # Check token usage and model
                if token_usage:
                    model_name = token_usage.get('model_name', 'unknown')
                    total_tokens = token_usage.get('total_tokens', 'unknown')
                    print(f"   Model used: {model_name}")
                    print(f"   Total tokens: {total_tokens}")
                    
                    # Verify correct model is being used
                    if 'apac.amazon.nova-lite-v1:0' in model_name or 'nova-lite' in model_name.lower():
                        print("   ✅ Correct Nova Lite model confirmed")
                    else:
                        print(f"   ⚠️ Unexpected model: {model_name}")
                
                return True
        else:
            print(f"   ❌ Query failed: {response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ Test failed: {e}")
        return False

def test_advanced_features():
    """Test advanced retrieval features."""
    print("\n3. Testing advanced retrieval features...")
    
    payload = {
        "question": "How to deploy alert rules in Kubernetes?",
        "retrieval_config": {
            "retriever_type": "hybrid",
            "use_mmr": True,
            "max_results": 5
        }
    }
    
    try:
        response = requests.post(
            "http://localhost:8888/query/advanced",
            json=payload,
            timeout=60
        )
        
        if response.status_code == 200:
            result = response.json()
            answer = result.get('answer', '')
            sources = result.get('sources', [])
            retrieval_method = result.get('retrieval_method', 'unknown')
            
            print(f"   Sources: {len(sources)}")
            print(f"   Retrieval method: {retrieval_method}")
            
            if 'AWS Bedrock model' not in answer:
                print("   ✅ Advanced features working")
                return True
            else:
                print("   ❌ Advanced features still have Bedrock issues")
                return False
        else:
            print(f"   ❌ Advanced test failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ Advanced test error: {e}")
        return False

def main():
    """Run all tests and provide final assessment."""
    print("🔧 QUERY ADVANCED ENDPOINT - FINAL VERIFICATION")
    print("=" * 60)
    
    basic_test = test_query_advanced_endpoint()
    advanced_test = test_advanced_features() if basic_test else False
    
    print("\n" + "=" * 60)
    print("📊 FINAL RESULTS:")
    print(f"   Basic Query Functionality: {'✅ WORKING' if basic_test else '❌ FAILED'}")
    print(f"   Advanced Retrieval Features: {'✅ WORKING' if advanced_test else '❌ FAILED'}")
    
    if basic_test and advanced_test:
        print("\n🎉 SUCCESS: Query Advanced endpoint is FULLY FUNCTIONAL!")
        print("   ✅ AWS Bedrock Nova Lite model working correctly")
        print("   ✅ Document retrieval working")
        print("   ✅ Advanced retrieval configurations working")
        print("   ✅ Token usage tracking working")
        print("\n   The endpoint is ready for production use!")
    elif basic_test:
        print("\n⚠️ PARTIAL SUCCESS: Basic functionality working")
        print("   ✅ AWS Bedrock LLM working")
        print("   ❌ Some advanced features may need attention")
    else:
        print("\n❌ FAILURE: Endpoint still has issues")
        print("   The QueryEngine may need manual restart or further debugging")

if __name__ == "__main__":
    main()
